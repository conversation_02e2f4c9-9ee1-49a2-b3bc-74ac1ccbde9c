import { useEffect, useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { useTranslation, type Language } from "@/lib/i18n";
import { Sparkles } from "lucide-react";

interface ProcessingModalProps {
  open: boolean;
  language: Language;
  onComplete: () => void;
}

export function ProcessingModal({ open, language, onComplete }: ProcessingModalProps) {
  const { t } = useTranslation(language);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (!open) {
      setProgress(0);
      return;
    }

    const interval = setInterval(() => {
      setProgress((prev) => {
        const increment = Math.random() * 15;
        const newProgress = Math.min(prev + increment, 100);
        
        if (newProgress >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            onComplete();
          }, 500);
        }
        
        return newProgress;
      });
    }, 200);

    return () => clearInterval(interval);
  }, [open, onComplete]);

  return (
    <Dialog open={open}>
      <DialogContent className="sm:max-w-md">
        <div className="text-center p-6">
          <div className="mb-6">
            <div className="gradient-bg rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 animate-spin">
              <Sparkles className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold mb-2">{t("processing.title")}</h3>
            <p className="text-gray-600">{t("processing.description")}</p>
          </div>
          
          <div className="space-y-4">
            <Progress value={progress} className="h-2" />
            <p className="text-sm text-gray-600">{Math.round(progress)}%</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
