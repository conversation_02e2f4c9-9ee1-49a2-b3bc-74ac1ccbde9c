<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义渐变 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#a855f7" /> <!-- --brand-purple: hsl(267, 84%, 72%) -->
      <stop offset="100%" stop-color="#ec4899" /> <!-- --brand-pink: hsl(328, 85%, 70%) -->
    </linearGradient>
  </defs>
  
  <!-- 圆角矩形背景 -->
  <rect width="32" height="32" rx="8" fill="url(#gradient)" />
  
  <!-- 剪刀图标 (来自 lucide-react Scissors 图标) -->
  <path d="M16 10C17.6569 10 19 8.65685 19 7C19 5.34315 17.6569 4 16 4C14.3431 4 13 5.34315 13 7C13 8.65685 14.3431 10 16 10Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  <path d="M16 28C17.6569 28 19 26.6569 19 25C19 23.3431 17.6569 22 16 22C14.3431 22 13 23.3431 13 25C13 26.6569 14.3431 28 16 28Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  <path d="M22 8L9.5 20.5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M9.5 11.5L22 24" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
