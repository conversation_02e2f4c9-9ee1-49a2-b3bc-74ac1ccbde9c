import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { useTranslation, type Language } from "@/lib/i18n";
import { X } from "lucide-react";

interface CookieConsentProps {
  language: Language;
}

export function CookieConsent({ language }: CookieConsentProps) {
  const { t } = useTranslation(language);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const hasConsent = localStorage.getItem('cookieConsent');
    if (!hasConsent) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem('cookieConsent', 'accepted');
    setIsVisible(false);
  };

  const handleClose = () => {
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 shadow-lg z-50">
      <div className="max-w-7xl mx-auto flex flex-col sm:flex-row items-center justify-between">
        <p className="text-sm text-gray-600 mb-4 sm:mb-0 pr-4">
          {t("cookies.message")} <a href="/cookies" className="text-purple-600 hover:underline">{t("cookies.policy")}</a>.
        </p>
        <div className="flex items-center space-x-4">
          <Button
            onClick={handleAccept}
            className="gradient-bg text-white font-medium px-6 py-2 rounded-lg hover:shadow-lg transition-all duration-200"
          >
            {t("cookies.accept")}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
