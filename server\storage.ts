import { users, processedImages, newsletters, type User, type InsertUser, type ProcessedImage, type InsertProcessedImage, type Newsletter, type InsertNewsletter } from "@shared/schema";

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Processed image methods
  getProcessedImage(id: number): Promise<ProcessedImage | undefined>;
  getProcessedImagesByUser(userId: number): Promise<ProcessedImage[]>;
  createProcessedImage(image: InsertProcessedImage): Promise<ProcessedImage>;
  updateProcessedImageStatus(id: number, status: string, processedPath?: string): Promise<ProcessedImage | undefined>;
  
  // Newsletter methods
  getNewsletterSubscriber(email: string): Promise<Newsletter | undefined>;
  createNewsletterSubscriber(newsletter: InsertNewsletter): Promise<Newsletter>;
  updateNewsletterSubscription(email: string, subscribed: boolean): Promise<Newsletter | undefined>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private processedImages: Map<number, ProcessedImage>;
  private newsletters: Map<number, Newsletter>;
  private currentUserId: number;
  private currentImageId: number;
  private currentNewsletterId: number;

  constructor() {
    this.users = new Map();
    this.processedImages = new Map();
    this.newsletters = new Map();
    this.currentUserId = 1;
    this.currentImageId = 1;
    this.currentNewsletterId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.username === username);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = {
      ...insertUser,
      id,
      createdAt: new Date(),
    };
    this.users.set(id, user);
    return user;
  }

  async getProcessedImage(id: number): Promise<ProcessedImage | undefined> {
    return this.processedImages.get(id);
  }

  async getProcessedImagesByUser(userId: number): Promise<ProcessedImage[]> {
    return Array.from(this.processedImages.values()).filter(image => image.userId === userId);
  }

  async createProcessedImage(insertImage: InsertProcessedImage): Promise<ProcessedImage> {
    const id = this.currentImageId++;
    const image: ProcessedImage = {
      id,
      userId: insertImage.userId ?? null,
      originalFilename: insertImage.originalFilename,
      originalPath: insertImage.originalPath,
      processedPath: insertImage.processedPath,
      processingStatus: insertImage.processingStatus ?? 'pending',
      createdAt: new Date(),
    };
    this.processedImages.set(id, image);
    return image;
  }

  async updateProcessedImageStatus(id: number, status: string, processedPath?: string): Promise<ProcessedImage | undefined> {
    const image = this.processedImages.get(id);
    if (!image) return undefined;
    
    const updatedImage = {
      ...image,
      processingStatus: status,
      ...(processedPath && { processedPath }),
    };
    this.processedImages.set(id, updatedImage);
    return updatedImage;
  }

  async getNewsletterSubscriber(email: string): Promise<Newsletter | undefined> {
    return Array.from(this.newsletters.values()).find(newsletter => newsletter.email === email);
  }

  async createNewsletterSubscriber(insertNewsletter: InsertNewsletter): Promise<Newsletter> {
    const id = this.currentNewsletterId++;
    const newsletter: Newsletter = {
      id,
      email: insertNewsletter.email,
      language: insertNewsletter.language ?? 'en',
      subscribed: true,
      createdAt: new Date(),
    };
    this.newsletters.set(id, newsletter);
    return newsletter;
  }

  async updateNewsletterSubscription(email: string, subscribed: boolean): Promise<Newsletter | undefined> {
    const newsletter = Array.from(this.newsletters.values()).find(n => n.email === email);
    if (!newsletter) return undefined;
    
    const updatedNewsletter = { ...newsletter, subscribed };
    this.newsletters.set(newsletter.id, updatedNewsletter);
    return updatedNewsletter;
  }
}

export const storage = new MemStorage();
