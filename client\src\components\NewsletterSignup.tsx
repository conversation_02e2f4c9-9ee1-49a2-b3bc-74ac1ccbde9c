import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTranslation, type Language } from "@/lib/i18n";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface NewsletterSignupProps {
  language: Language;
}

export function NewsletterSignup({ language }: NewsletterSignupProps) {
  const { t } = useTranslation(language);
  const { toast } = useToast();
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) return;
    
    setIsSubmitting(true);
    
    try {
      await apiRequest('POST', '/api/newsletter', {
        email,
        language,
      });
      
      toast({
        title: "Successfully subscribed!",
        description: "Thank you for subscribing to our newsletter.",
      });
      
      setEmail("");
    } catch (error) {
      toast({
        title: "Subscription failed",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="py-16 lg:py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            {t("newsletter.title")}
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            {t("newsletter.description")}
          </p>
          
          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <Input
              type="email"
              placeholder={t("newsletter.email_placeholder")}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="flex-1"
              required
            />
            <Button
              type="submit"
              disabled={isSubmitting}
              className="gradient-bg text-white font-semibold px-8 py-3 rounded-lg hover:shadow-lg transition-all duration-200 transform hover:-translate-y-0.5"
            >
              {isSubmitting ? "Subscribing..." : t("newsletter.subscribe")}
            </Button>
          </form>
          
          <p className="text-sm text-gray-600 mt-6">
            {t("newsletter.privacy_notice")} <a href="/privacy" className="text-purple-600 hover:underline">{t("newsletter.privacy_policy")}</a>.
          </p>
        </div>
      </div>
    </section>
  );
}
