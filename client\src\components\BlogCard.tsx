import { Card, CardContent } from "@/components/ui/card";
import { <PERSON> } from "wouter";
import { type BlogPost } from "@/lib/types";

interface BlogCardProps {
  post: BlogPost;
}

export function BlogCard({ post }: BlogCardProps) {
  return (
    <Card className="shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
      <img
        src={post.image}
        alt={post.title}
        className="w-full h-48 object-cover"
      />
      <CardContent className="p-6">
        <div className="text-sm text-gray-600 mb-2">{post.date}</div>
        <h3 className="text-xl font-semibold mb-3 hover:text-purple-600 transition-colors">
          <Link href={`/blog/${post.slug}`}>
            {post.title}
          </Link>
        </h3>
        <p className="text-gray-600 mb-4">{post.excerpt}</p>
      </CardContent>
    </Card>
  );
}
