<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .result {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <h1>Test Image Upload</h1>
    <p>This page tests the improved image upload functionality with support for multiple formats:</p>
    <ul>
        <li>JPG/JPEG</li>
        <li>PNG</li>
        <li>GIF</li>
        <li>WebP</li>
        <li>BMP</li>
        <li>TIFF</li>
        <li>SVG</li>
    </ul>

    <div class="upload-area" id="uploadArea">
        <p>Drag and drop an image here or click to select</p>
        <input type="file" id="fileInput" accept=".jpg,.jpeg,.png,.gif,.webp,.bmp,.tiff,.tif,.svg,image/*" style="display: none;">
        <button onclick="document.getElementById('fileInput').click()">Select Image</button>
    </div>

    <div id="result"></div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const result = document.getElementById('result');

        // Supported formats
        const SUPPORTED_IMAGE_TYPES = [
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/gif',
            'image/webp',
            'image/bmp',
            'image/tiff',
            'image/tif',
            'image/svg+xml'
        ];

        const SUPPORTED_IMAGE_EXTENSIONS = [
            '.jpg',
            '.jpeg',
            '.png',
            '.gif',
            '.webp',
            '.bmp',
            '.tiff',
            '.tif',
            '.svg'
        ];

        function showResult(message, type) {
            result.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function validateFile(file) {
            // Check MIME type
            const isSupportedMimeType = SUPPORTED_IMAGE_TYPES.includes(file.type.toLowerCase());
            
            // Check file extension as fallback
            const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
            const isSupportedExtension = SUPPORTED_IMAGE_EXTENSIONS.includes(fileExtension);
            
            // Also check if mimetype starts with 'image/' for broader compatibility
            const isImageMimeType = file.type.startsWith('image/');
            
            if (!(isSupportedMimeType || (isImageMimeType && isSupportedExtension))) {
                return `Unsupported file type. File: ${file.name}, Type: ${file.type}, Extension: ${fileExtension}. Supported formats: ${SUPPORTED_IMAGE_EXTENSIONS.join(', ')}`;
            }

            if (file.size > 10 * 1024 * 1024) {
                return `File too large: ${(file.size / 1024 / 1024).toFixed(2)}MB. Maximum size: 10MB`;
            }

            return null;
        }

        async function uploadFile(file) {
            const error = validateFile(file);
            if (error) {
                showResult(error, 'error');
                return;
            }

            showResult(`Uploading ${file.name} (${file.type}, ${(file.size / 1024).toFixed(2)}KB)...`, 'info');

            try {
                const formData = new FormData();
                formData.append('image', file);

                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`Upload successful! Processing ID: ${result.id}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`Upload failed: ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`Upload error: ${error.message}`, 'error');
            }
        }

        // File input change handler
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                uploadFile(file);
            }
        });

        // Drag and drop handlers
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const file = e.dataTransfer.files[0];
            if (file) {
                uploadFile(file);
            }
        });

        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
    </script>
</body>
</html>
