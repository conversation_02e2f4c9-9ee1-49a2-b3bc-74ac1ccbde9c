<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义渐变 -->
  <defs>
    <linearGradient id="gradient32" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#a855f7" />
      <stop offset="100%" stop-color="#ec4899" />
    </linearGradient>
  </defs>
  
  <!-- 圆角矩形背景 -->
  <rect width="32" height="32" rx="8" fill="url(#gradient32)" />
  
  <!-- 剪刀图标 -->
  <circle cx="16" cy="8" r="3" stroke="white" stroke-width="2" fill="none"/>
  <circle cx="16" cy="24" r="3" stroke="white" stroke-width="2" fill="none"/>
  <path d="M22 8L10 20" stroke="white" stroke-width="2" stroke-linecap="round"/>
  <path d="M10 12L22 24" stroke="white" stroke-width="2" stroke-linecap="round"/>
</svg>
