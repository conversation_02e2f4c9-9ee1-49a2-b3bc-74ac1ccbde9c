import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { type Language } from "@/lib/i18n";

interface LanguageSwitcherProps {
  language: Language;
  onLanguageChange: (language: Language) => void;
}

export function LanguageSwitcher({ language, onLanguageChange }: LanguageSwitcherProps) {
  const languages = [
    { code: 'en', name: '🇺🇸 English', label: 'English' },
    { code: 'zh', name: '🇨🇳 中文', label: '中文' },
    { code: 'es', name: '🇪🇸 Español', label: 'Español' },
    { code: 'fr', name: '🇫🇷 Français', label: 'Français' },
    { code: 'de', name: '🇩🇪 Deutsch', label: 'Deuts<PERSON>' },
  ];

  return (
    <Select value={language} onValueChange={(value) => onLanguageChange(value as Language)}>
      <SelectTrigger className="w-[140px] border-gray-300">
        <SelectValue placeholder="Language" />
      </SelectTrigger>
      <SelectContent>
        {languages.map((lang) => (
          <SelectItem key={lang.code} value={lang.code}>
            {lang.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
