import React from "react";
import { useLocation } from "wouter";
import { useTranslation, type Language } from "@/lib/i18n";

interface SEOHeadProps {
  title?: string;
  description?: string;
  language: Language;
  canonical?: string;
}

export function SEOHead({ title, description, language, canonical }: SEOHeadProps) {
  const [location] = useLocation();
  const { t } = useTranslation(language);
  
  const defaultTitle = t("hero.title.line1") + " - " + t("hero.title.line2");
  const defaultDescription = t("hero.description");
  
  const finalTitle = title || defaultTitle;
  const finalDescription = description || defaultDescription;
  const finalCanonical = canonical || `https://www.remove.bg${location}`;
  
  // Update document title and meta tags
  React.useEffect(() => {
    document.title = finalTitle;
    
    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', finalDescription);
    }
    
    // Update language
    document.documentElement.setAttribute('lang', language);
    
    // Update canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]');
    if (!canonicalLink) {
      canonicalLink = document.createElement('link');
      canonicalLink.setAttribute('rel', 'canonical');
      document.head.appendChild(canonicalLink);
    }
    canonicalLink.setAttribute('href', finalCanonical);
    
    // Update Open Graph tags
    let ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle) {
      ogTitle.setAttribute('content', finalTitle);
    }
    
    let ogDescription = document.querySelector('meta[property="og:description"]');
    if (ogDescription) {
      ogDescription.setAttribute('content', finalDescription);
    }
    
    let ogUrl = document.querySelector('meta[property="og:url"]');
    if (ogUrl) {
      ogUrl.setAttribute('content', finalCanonical);
    }
    
    // Update Twitter Card tags
    let twitterTitle = document.querySelector('meta[name="twitter:title"]');
    if (twitterTitle) {
      twitterTitle.setAttribute('content', finalTitle);
    }
    
    let twitterDescription = document.querySelector('meta[name="twitter:description"]');
    if (twitterDescription) {
      twitterDescription.setAttribute('content', finalDescription);
    }
  }, [finalTitle, finalDescription, finalCanonical, language]);
  
  return null;
}
