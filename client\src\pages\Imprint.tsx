import { useTranslation, type Language } from "@/lib/i18n";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Building, Mail, Phone, Globe, ArrowLeft } from "lucide-react";
import { Link } from "wouter";

interface ImprintProps {
  language: Language;
}

export default function Imprint({ language }: ImprintProps) {
  const { t } = useTranslation(language);

  return (
    <div className="min-h-screen bg-gray-50 py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              {t("imprint.page.back")}
            </Button>
          </Link>
        </div>
        {/* Header */}
        <div className="text-center mb-16">
          <div className="gradient-bg rounded-lg w-16 h-16 flex items-center justify-center mx-auto mb-6">
            <Building className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            {t("imprint.page.title")}
          </h1>
          <p className="text-xl text-gray-600">
            {t("imprint.page.description")}
          </p>
        </div>

        <div className="space-y-8">
          {/* Company Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building className="h-6 w-6 text-purple-600" />
                <span>{t("imprint.page.company_info")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">{t("imprint.page.company_name")}</h3>
              </div>
              <div>
                <h4 className="font-medium">Address:</h4>
                <p className="text-gray-600 whitespace-pre-line">
                  {t("imprint.page.address")}
                </p>
              </div>
              <div>
                <h4 className="font-medium">{t("imprint.page.registration")}</h4>
              </div>
              <div>
                <h4 className="font-medium">{t("imprint.page.vat")}</h4>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Mail className="h-6 w-6 text-purple-600" />
                <span>{t("imprint.page.contact_info")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-gray-600">{t("imprint.page.email")}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-gray-600">{t("imprint.page.phone")}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Globe className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-gray-600">{t("imprint.page.website")}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Legal Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t("imprint.page.legal_info")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium">{t("imprint.page.responsible")}</h4>
                <p className="text-gray-600">{t("imprint.page.responsible_name")}</p>
              </div>
            </CardContent>
          </Card>

          {/* Disclaimer */}
          <Card>
            <CardHeader>
              <CardTitle>{t("imprint.page.disclaimer")}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                {t("imprint.page.disclaimer_content")}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}