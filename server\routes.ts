import type { Express } from "express";
import { createServer, type Server } from "http";
import multer from "multer";
import path from "path";
import fs from "fs";
import express from "express";
import { storage } from "./storage";
import { insertProcessedImageSchema, insertNewsletterSchema } from "@shared/schema";
import { z } from "zod";
import sharp from "sharp";

// Supported image formats
const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/bmp',
  'image/tiff',
  'image/tif',
  'image/svg+xml'
];

const SUPPORTED_IMAGE_EXTENSIONS = [
  '.jpg',
  '.jpeg',
  '.png',
  '.gif',
  '.webp',
  '.bmp',
  '.tiff',
  '.tif',
  '.svg'
];

const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    console.log('File upload attempt:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size
    });

    // Check MIME type
    const isSupportedMimeType = SUPPORTED_IMAGE_TYPES.includes(file.mimetype.toLowerCase());

    // Check file extension as fallback
    const fileExtension = path.extname(file.originalname).toLowerCase();
    const isSupportedExtension = SUPPORTED_IMAGE_EXTENSIONS.includes(fileExtension);

    // Also check if mimetype starts with 'image/' for broader compatibility
    const isImageMimeType = file.mimetype.startsWith('image/');

    if (isSupportedMimeType || (isImageMimeType && isSupportedExtension)) {
      console.log('File accepted:', file.originalname);
      cb(null, true);
    } else {
      console.log('File rejected:', {
        originalname: file.originalname,
        mimetype: file.mimetype,
        extension: fileExtension
      });
      cb(new Error(`Unsupported file type. Supported formats: ${SUPPORTED_IMAGE_EXTENSIONS.join(', ')}`));
    }
  },
});

// Function to perform basic background removal using image processing
async function simulateBackgroundRemoval(imageId: number, originalPath: string, filename: string) {
  try {
    console.log(`Processing background removal for image ${imageId}`);

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));

    const processedPath = `processed_${filename}`;
    const outputPath = path.join('uploads', processedPath);

    // Perform basic background removal using Sharp
    await removeBackgroundBasic(originalPath, outputPath);

    // Update status to completed
    await storage.updateProcessedImageStatus(imageId, 'completed', processedPath);
    console.log(`Background removal completed for image ${imageId}`);
  } catch (error) {
    console.error('Background removal error:', error);
    await storage.updateProcessedImageStatus(imageId, 'failed');
  }
}

// Basic background removal function using Sharp
async function removeBackgroundBasic(inputPath: string, outputPath: string) {
  try {
    // Read the image
    const image = sharp(inputPath);
    const { width, height } = await image.metadata();

    if (!width || !height) {
      throw new Error('Unable to get image dimensions');
    }

    // Get image data as raw buffer
    const { data, info } = await image
      .raw()
      .toBuffer({ resolveWithObject: true });

    // Create a new buffer for the output with alpha channel
    const outputData = Buffer.alloc(width * height * 4); // RGBA

    // Simple background removal algorithm
    // This is a basic implementation - detects edges and removes background
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * info.channels;
        const outIdx = (y * width + x) * 4;

        const r = data[idx];
        const g = data[idx + 1];
        const b = data[idx + 2];

        // Simple edge detection and background removal
        // Keep pixels that are likely to be foreground (center region, high contrast)
        const centerX = width / 2;
        const centerY = height / 2;
        const distanceFromCenter = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
        const maxDistance = Math.sqrt(centerX ** 2 + centerY ** 2);
        const centerWeight = 1 - (distanceFromCenter / maxDistance);

        // Calculate pixel intensity
        const intensity = (r + g + b) / 3;

        // Simple background detection (edges and low contrast areas)
        const isEdge = x < 10 || x > width - 10 || y < 10 || y > height - 10;
        const isLowContrast = intensity > 200 || intensity < 50;

        // Determine if pixel should be transparent
        let alpha = 255;
        if (isEdge || (isLowContrast && centerWeight < 0.3)) {
          alpha = 0; // Make background transparent
        } else if (centerWeight < 0.6) {
          alpha = Math.floor(centerWeight * 255); // Gradual transparency
        }

        // Set RGBA values
        outputData[outIdx] = r;     // Red
        outputData[outIdx + 1] = g; // Green
        outputData[outIdx + 2] = b; // Blue
        outputData[outIdx + 3] = alpha; // Alpha
      }
    }

    // Create output image with transparency
    await sharp(outputData, {
      raw: {
        width,
        height,
        channels: 4
      }
    })
    .png()
    .toFile(outputPath);

    console.log('Basic background removal completed');
  } catch (error) {
    console.error('Error in background removal:', error);
    // Fallback: just copy the original file
    fs.copyFileSync(inputPath, outputPath);
  }
}

// Function to process image with remove.bg API
async function processImageWithRemoveBg(imageId: number, originalPath: string, filename: string) {
  try {
    const apiKey = process.env.REMOVE_BG_API_KEY;

    // If no API key is configured, use simulation mode
    if (!apiKey) {
      console.log('No remove.bg API key configured, using simulation mode');
      return simulateBackgroundRemoval(imageId, originalPath, filename);
    }

    console.log(`Processing image ${imageId} with remove.bg API`);

    const FormData = await import('form-data');
    const form = new FormData.default();

    form.append('image_file', fs.createReadStream(originalPath));
    form.append('size', 'auto');

    const options = {
      method: 'POST',
      hostname: 'api.remove.bg',
      path: '/v1.0/removebg',
      headers: {
        'X-Api-Key': apiKey,
        ...form.getHeaders(),
      },
    };

    const https = await import('https');
    const req = https.request(options, (res) => {
      const processedPath = `processed_${filename}`;
      const outputPath = path.join('uploads', processedPath);

      if (res.statusCode === 200) {
        const fileStream = fs.createWriteStream(outputPath);
        res.pipe(fileStream);

        fileStream.on('finish', () => {
          storage.updateProcessedImageStatus(imageId, 'completed', processedPath);
          console.log(`Real background removal completed for image ${imageId}`);
        });

        fileStream.on('error', (error) => {
          console.error('File write error:', error);
          storage.updateProcessedImageStatus(imageId, 'failed');
        });
      } else {
        console.error('Remove bg API error:', res.statusCode);
        storage.updateProcessedImageStatus(imageId, 'failed');
      }
    });

    req.on('error', (error) => {
      console.error('Request error:', error);
      storage.updateProcessedImageStatus(imageId, 'failed');
    });

    form.pipe(req);
  } catch (error) {
    console.error('Processing error:', error);
    storage.updateProcessedImageStatus(imageId, 'failed');
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Ensure uploads directory exists
  if (!fs.existsSync('uploads')) {
    fs.mkdirSync('uploads', { recursive: true });
  }

  // Serve test files
  app.use(express.static('.', {
    index: false,
    setHeaders: (res, path) => {
      if (path.endsWith('.html')) {
        res.setHeader('Content-Type', 'text/html');
      }
    }
  }));

  // Serve uploaded files with proper headers
  app.use('/uploads', express.static('uploads', {
    setHeaders: (res, filepath) => {
      if (filepath.match(/\.(jpg|jpeg|png|gif|webp|bmp|tiff|tif|svg)$/i) || filepath.includes('processed_')) {
        const ext = path.extname(filepath).toLowerCase();
        switch (ext) {
          case '.png':
            res.setHeader('Content-Type', 'image/png');
            break;
          case '.gif':
            res.setHeader('Content-Type', 'image/gif');
            break;
          case '.webp':
            res.setHeader('Content-Type', 'image/webp');
            break;
          case '.bmp':
            res.setHeader('Content-Type', 'image/bmp');
            break;
          case '.tiff':
          case '.tif':
            res.setHeader('Content-Type', 'image/tiff');
            break;
          case '.svg':
            res.setHeader('Content-Type', 'image/svg+xml');
            break;
          case '.jpg':
          case '.jpeg':
          default:
            res.setHeader('Content-Type', 'image/jpeg');
            break;
        }
      }
    }
  }));

  // Upload and process image
  app.post('/api/upload', upload.single('image'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'No image file provided' });
      }

      const { originalname, filename, path: filepath } = req.file;
      
      // Create processed image record
      const imageData = {
        userId: null, // Anonymous upload
        originalFilename: originalname,
        originalPath: filepath,
        processedPath: '', // Will be updated after processing
        processingStatus: 'pending',
      };

      const processedImage = await storage.createProcessedImage(imageData);

      // Process the image with remove.bg API
      processImageWithRemoveBg(processedImage.id, filepath, filename);

      res.json({
        id: processedImage.id,
        status: 'pending',
        message: 'Image uploaded successfully. Processing started.',
      });
    } catch (error) {
      console.error('Upload error:', error);
      res.status(500).json({ message: 'Upload failed' });
    }
  });

  // Check processing status
  app.get('/api/process/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const processedImage = await storage.getProcessedImage(id);

      if (!processedImage) {
        return res.status(404).json({ message: 'Image not found' });
      }

      // Check if we're in simulation mode
      const isSimulationMode = !process.env.REMOVE_BG_API_KEY;

      res.json({
        id: processedImage.id,
        status: processedImage.processingStatus,
        originalPath: processedImage.originalPath,
        processedPath: processedImage.processedPath,
        isSimulationMode: isSimulationMode
      });
    } catch (error) {
      console.error('Status check error:', error);
      res.status(500).json({ message: 'Failed to check status' });
    }
  });

  // Download processed image
  app.get('/api/download/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const processedImage = await storage.getProcessedImage(id);
      
      if (!processedImage || processedImage.processingStatus !== 'completed') {
        return res.status(404).json({ message: 'Processed image not found' });
      }

      const filePath = path.join('uploads', processedImage.processedPath);
      
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({ message: 'File not found' });
      }

      res.download(filePath, `processed_${processedImage.originalFilename}`);
    } catch (error) {
      console.error('Download error:', error);
      res.status(500).json({ message: 'Download failed' });
    }
  });

  // Newsletter subscription
  app.post('/api/newsletter', async (req, res) => {
    try {
      const validatedData = insertNewsletterSchema.parse(req.body);
      
      // Check if already subscribed
      const existing = await storage.getNewsletterSubscriber(validatedData.email);
      if (existing) {
        if (existing.subscribed) {
          return res.status(400).json({ message: 'Already subscribed' });
        } else {
          // Reactivate subscription
          await storage.updateNewsletterSubscription(validatedData.email, true);
          return res.json({ message: 'Subscription reactivated' });
        }
      }

      await storage.createNewsletterSubscriber(validatedData);
      res.json({ message: 'Successfully subscribed to newsletter' });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: 'Invalid data', errors: error.errors });
      }
      console.error('Newsletter subscription error:', error);
      res.status(500).json({ message: 'Subscription failed' });
    }
  });

  // Unsubscribe from newsletter
  app.post('/api/newsletter/unsubscribe', async (req, res) => {
    try {
      const { email } = req.body;
      
      if (!email) {
        return res.status(400).json({ message: 'Email is required' });
      }

      const updated = await storage.updateNewsletterSubscription(email, false);
      
      if (!updated) {
        return res.status(404).json({ message: 'Email not found' });
      }

      res.json({ message: 'Successfully unsubscribed' });
    } catch (error) {
      console.error('Unsubscribe error:', error);
      res.status(500).json({ message: 'Unsubscribe failed' });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
