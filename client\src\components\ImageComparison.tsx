import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useTranslation, type Language } from "@/lib/i18n";
import { Download, Plus } from "lucide-react";

interface ImageComparisonProps {
  language: Language;
  originalImage: string;
  processedImage: string;
  onProcessAnother: () => void;
  onDownload: () => void;
}

export function ImageComparison({
  language,
  originalImage,
  processedImage,
  onProcessAnother,
  onDownload,
}: ImageComparisonProps) {
  const { t } = useTranslation(language);

  return (
    <div className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">{t("results.title")}</h2>
          <p className="text-gray-600">{t("results.description")}</p>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8 mb-8">
            {/* Original Image */}
            <Card className="shadow-lg">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4 text-center">
                  {t("results.original")}
                </h3>
                <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                  <img
                    src={originalImage}
                    alt="Original image"
                    className="max-w-full max-h-full object-contain"
                  />
                </div>
              </CardContent>
            </Card>
            
            {/* Processed Image */}
            <Card className="shadow-lg">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4 text-center">
                  {t("results.transparent")}
                </h3>
                <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden relative checkerboard">
                  <img
                    src={processedImage}
                    alt="Processed image"
                    className="max-w-full max-h-full object-contain"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Download Options */}
          <div className="text-center">
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-6">
              <Button
                onClick={onDownload}
                className="gradient-bg text-white font-semibold px-8 py-4 rounded-xl hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1"
              >
                <Download className="h-4 w-4 mr-2" />
                {t("results.download.hd")}
              </Button>
            </div>
            <Button
              variant="ghost"
              onClick={onProcessAnother}
              className="text-purple-600 hover:underline"
            >
              <Plus className="h-4 w-4 mr-2" />
              {t("results.another")}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
