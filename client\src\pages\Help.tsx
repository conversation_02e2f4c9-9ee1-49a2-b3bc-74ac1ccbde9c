import { useTranslation, type Language } from "@/lib/i18n";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Search, HelpCircle, Book, MessageCircle, ArrowLeft } from "lucide-react";
import { Link } from "wouter";

interface HelpProps {
  language: Language;
}

export default function Help({ language }: HelpProps) {
  const { t } = useTranslation(language);

  const helpCategories = [
    {
      title: t("help.getting_started"),
      description: t("help.getting_started_desc"),
      icon: Book,
      articles: [
        t("help.how_to_upload"),
        t("help.supported_formats"),
        t("help.download_results"),
      ]
    },
    {
      title: t("help.troubleshooting"),
      description: t("help.troubleshooting_desc"),
      icon: HelpCircle,
      articles: [
        t("help.image_quality"),
        t("help.processing_failed"),
        t("help.slow_processing"),
      ]
    },
    {
      title: t("help.account"),
      description: t("help.account_desc"),
      icon: MessageCircle,
      articles: [
        t("help.create_account"),
        t("help.manage_subscription"),
        t("help.billing_questions"),
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              {t("help.back_to_home")}
            </Button>
          </Link>
        </div>
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            {t("help.title")}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            {t("help.description")}
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="搜索帮助文档..."
              className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Help Categories */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {helpCategories.map((category, index) => {
            const IconComponent = category.icon;
            return (
              <Card key={index} className="h-full hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="gradient-bg rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                    <IconComponent className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle className="text-xl">{category.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-6">{category.description}</p>
                  <ul className="space-y-2">
                    {category.articles.map((article, articleIndex) => (
                      <li key={articleIndex}>
                        <a
                          href="#"
                          className="text-purple-600 hover:text-purple-800 transition-colors"
                        >
                          {article}
                        </a>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Contact Support */}
        <div className="text-center">
          <Card className="max-w-2xl mx-auto">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">{t("help.contact_title")}</h2>
              <p className="text-gray-600 mb-6">{t("help.contact_description")}</p>
              <Button className="gradient-bg text-white">
                {t("help.contact_button")}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}